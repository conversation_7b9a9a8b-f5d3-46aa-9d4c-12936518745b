#include <iostream>
#include <string>
#include <vector>
using namespace std;

class Pet {
    string name;
    string breed;
    int age;
public:
    Pet(string n, string b, int a) : name(n), breed(b), age(a) {}
    void showInfo() {
        cout << "Name: " << name << ", Breed: " << breed << ", Age: " << age << endl;
    }
};

class Dog : public Pet {
public:
    Dog(string n, string b, int a) : Pet(n, b, a) {}
    void showInfo() {
        cout << "Dog - ";
        Pet::showInfo();
    }
};

class Cat : public Pet {
public:
    Cat(string n, string b, int a) : Pet(n, b, a) {}
    void showInfo() {
        cout << "Cat - ";
        Pet::showInfo();
    }
};

class Parrot : public Pet {
public:
    Parrot(string n, string b, int a) : Pet(n, b, a) {}
    void showInfo() {
        cout << "Parrot - ";
        Pet::showInfo();
    }
};

class Student {
protected:
    string name;
    int course;
    string university;
public:
    Student(string n, int c, string u) : name(n), course(c), university(u) {}
    virtual void showInfo() {
        cout << "Student: " << name << ", Course: " << course << ", University: " << university << endl;
    }
};

class Aspirant : public Student {
    string dissertationTopic;
public:
    Aspirant(string n, int c, string u, string topic) : Student(n, c, u), dissertationTopic(topic) {}
    void showInfo() override {
        cout << "Aspirant: " << name << ", Course: " << course << ", University: " << university << ", Topic: " << dissertationTopic << endl;
    }
};

class Passport {
protected:
    string fullName;
    string birthDate;
    string idNumber;
public:
    Passport(string name, string bDate, string id) : fullName(name), birthDate(bDate), idNumber(id) {}
    virtual void showInfo() {
        cout << "Full Name: " << fullName << ", Birthdate: " << birthDate << ", ID: " << idNumber << endl;
    }
};

class ForeignPassport : public Passport {
    string foreignID;
    vector<string> visas;
public:
    ForeignPassport(string name, string bDate, string id, string fID) : Passport(name, bDate, id), foreignID(fID) {}

    void addVisa(string visa) {
        visas.push_back(visa);
    }

    void showInfo() override {
        Passport::showInfo();
        cout << "Foreign ID: " << foreignID << endl;
        cout << "Visas: ";
        for (auto v : visas) {
            cout << v << "; ";
        }
        cout << endl;
    }
};


int main() {
    Dog dog("Rex", "Labrador", 3);
    Cat cat("Tom", "Siamese", 2);
    Parrot parrot("Kesha", "Ara", 1);
    dog.showInfo();
    cat.showInfo();
    parrot.showInfo();

    Student st("George", 2, "MIT");
    Aspirant as("John", 3, "Harvard", "Math");
    st.showInfo();
    as.showInfo();
    return 0;
}