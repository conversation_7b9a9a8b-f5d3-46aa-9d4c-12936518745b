#include <iostream>
#include <string>
#include <windows.h>
using namespace std;

class Car {
private:
    string model_name;
    int year_production;
    class Engine {
    public:
        bool isWork = false;
        int power;
        int V;
        Engine(int HPower, int VCylindr) : power{ HPower }, V{ VCylindr } {}

        void start_engine() {
            isWork = true;
            cout << "Engine started" << endl;
        }

        void stop_engine() {
            isWork = false;
            cout << "Engine stopped" << endl;
        }
    };

    Engine* engine;

public:
    Car(string name, int year, int power, int V) : model_name(name), year_production(year), engine(nullptr) {
        engine = new Engine(power, V);
    }

    ~Car() {
        delete engine;
        cout << "Car deleted" << endl;
    }

    void trip(int distance) {
        engine->start_engine();
        cout << "Trip started" << endl;
        Sleep(distance * 1000);
        engine->stop_engine();
        cout << "Trip finished" << endl;
    }

    void start_car() {
        engine->start_engine();
    }

    void stop_car() {
        engine->stop_engine();
    }
};

int main() {
    Car car("BMW", 2020, 300, 3.0);
    car.trip(10);
    return 0;
}